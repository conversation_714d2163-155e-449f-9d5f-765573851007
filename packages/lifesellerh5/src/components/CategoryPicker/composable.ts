import { ref, computed, watch } from 'vue'
import { getMarketableCategories } from '~/services/edith_get_marketable_categories'
import { getChildrenCategories } from '~/services/edith_get_children_categories'
import {
  ICategory,
  CategoryPickerValue,
  CategoryItem,
  LevelData,
  CategoryPickerProps,
  CategoryPickerEmits
} from './types'

export function useCategoryPicker(props: CategoryPickerProps, emit: CategoryPickerEmits) {
  // 响应式状态
  const loading = ref(false)
  const error = ref('')
  const visible = ref(false)

  // 级联数据 - 支持多层级
  const levelsData = ref<LevelData[]>([])
  const currentLevel = ref(0) // 当前展示的层级

  // 当前选中值（数组形式）
  const currentValue = ref<CategoryPickerValue>([])

  // 最大层级数
  const maxLevels = computed(() => props.maxLevels || 3)

  // 网格列数
  const gridColumns = computed(() => props.gridColumns || 3)

  // 初始化数据
  const initializeData = async () => {
    try {
      loading.value = true
      error.value = ''

      const response = await getMarketableCategories({})

      if (!response) {
        throw new Error('API 响应为空')
      }

      const categories = response.itemInfos || []

      // 初始化第一层数据
      levelsData.value = [{
        level: 1,
        categories,
        selectedId: undefined,
        selectedIds: undefined
      }]

      // 如果有数据，自动选中第一个并继续加载后续层级
      if (categories.length > 0) {
        await autoSelectFirstOptions()
      }

      currentLevel.value = 0
    } catch (err: any) {
      error.value = err.message || '加载分类数据失败'
    } finally {
      loading.value = false
    }
  }

  // 自动选中每层第一个选项
  const autoSelectFirstOptions = async () => {
    let currentLevelIndex = 0

    while (currentLevelIndex < maxLevels.value && currentLevelIndex < levelsData.value.length) {
      const currentLevelData = levelsData.value[currentLevelIndex]

      if (currentLevelData.categories.length === 0) break

      // 选中第一个选项
      const firstCategory = currentLevelData.categories[0]
      currentLevelData.selectedId = firstCategory.id

      // 如果是叶子节点或已达到最大层级，停止
      if (firstCategory.isLeaf || currentLevelIndex + 1 >= maxLevels.value) {
        break
      }

      // 加载下一层数据
      try {
        // eslint-disable-next-line no-await-in-loop
        const response = await getChildrenCategories({ categoryId: firstCategory.id || '' })

        if (response.childrenInfos && response.childrenInfos.length > 0) {
          const nextLevelData: LevelData = {
            level: currentLevelIndex + 2,
            categories: response.childrenInfos,
            selectedId: undefined,
            selectedIds: undefined
          }

          if (levelsData.value.length <= currentLevelIndex + 1) {
            levelsData.value.push(nextLevelData)
          } else {
            levelsData.value[currentLevelIndex + 1] = nextLevelData
          }

          currentLevelIndex += 1
        } else {
          // 没有子分类，停止
          break
        }
      } catch (err) {
        // 加载失败，停止
        console.warn('加载子分类失败:', err)
        break
      }
    }

    // 自动选择完成后，同步更新 currentValue
    updateCurrentValueFromLevelsData()
  }

  // 加载子分类数据
  const loadChildrenCategories = async (categoryId: string, targetLevel: number) => {
    const response = await getChildrenCategories({ categoryId })

    if (response.childrenInfos && response.childrenInfos.length > 0) {
      const levelData: LevelData = {
        level: targetLevel,
        categories: response.childrenInfos,
        selectedId: undefined,
        selectedIds: undefined
      }

      const targetIndex = targetLevel - 1
      if (levelsData.value.length <= targetIndex) {
        levelsData.value.push(levelData)
      } else {
        levelsData.value[targetIndex] = levelData
      }
    }
  }

  // 根据初始值加载对应的子分类
  const initializeFromValue = async (value: CategoryPickerValue) => {
    if (!value || value.length === 0) return

    try {
      loading.value = true
      error.value = ''

      // 初始化第一层数据
      await initializeData()

      // 按层级设置选中状态并加载下层数据
      for (let i = 0; i < value.length && i < maxLevels.value; i++) {
        const categoryItem = value[i]
        if (!categoryItem || !categoryItem.id) {
          // eslint-disable-next-line no-continue
          continue
        }

        // 设置当前层级的选中状态
        if (levelsData.value[i]) {
          levelsData.value[i].selectedId = categoryItem.id
        }

        // 如果不是最后一层，加载下层数据（不管下一个值是否存在）
        if (i < maxLevels.value - 1) {
          // eslint-disable-next-line no-await-in-loop
          await loadChildrenCategories(categoryItem.id, i + 2)
        }
      }

      // 更新当前层级到最后一个有数据的层级
      currentLevel.value = Math.max(0, levelsData.value.length - 1)
    } catch (err: any) {
      error.value = err.message || '初始化分类数据失败'
    } finally {
      loading.value = false
    }
  }

  // 监听外部值变化
  watch(
    () => props.modelValue,
    async val => {
      currentValue.value = val || []
      // 如果有值，基于值初始化层级数据
      if (val && val.length > 0) {
        await initializeFromValue(val)
      }
    },
    { immediate: true }
  )

  // 判断指定层级是否为最后一层
  const isLastLevelByNumber = (level: number): boolean => level === maxLevels.value

  // 是否支持多选（只有最后一层支持）
  const isMultipleMode = computed(() => props.lastLevelMultiple)

  // 显示文本 - 直接从 modelValue 中提取分类名称
  const displayText = computed(() => {
    // 只基于 props.modelValue 显示已确认的值
    if (!props.modelValue || props.modelValue.length === 0) {
      return props.placeholder || '请选择'
    }

    // 直接从 CategoryItem 数组中提取名称并拼接
    const names = props.modelValue.map(item => item.name).filter(name => name)

    if (names.length === 0) {
      return props.placeholder || '请选择'
    }

    return names.join(' / ')
  })

  // 构建分类值数组
  const buildCategoryValue = (): CategoryPickerValue => {
    const result: CategoryItem[] = []

    // 遍历所有层级，构建完整路径
    levelsData.value.forEach(levelData => {
      if (levelData.selectedId) {
        const category = levelData.categories.find(cat => cat.id === levelData.selectedId)
        if (category && category.id && category.name) {
          result.push({
            id: category.id,
            name: category.name
          })
        }
      }
    })

    return result
  }

  // 构建多选值
  const buildMultipleValue = (): CategoryPickerValue => {
    const lastLevel = levelsData.value[levelsData.value.length - 1]
    if (lastLevel && lastLevel.selectedIds && lastLevel.selectedIds.length > 0) {
      // 前面层级的选中项 + 最后一层的多选项
      const baseResult = buildCategoryValue().slice(0, -1)
      const lastLevelItems: CategoryItem[] = lastLevel.selectedIds.map(id => {
        const category = lastLevel.categories.find(cat => cat.id === id)
        return {
          id,
          name: category?.name || ''
        }
      }).filter(item => item.name)

      return [...baseResult, ...lastLevelItems]
    }
    return buildCategoryValue()
  }

  // 根据 levelsData 的选中状态更新 currentValue
  const updateCurrentValueFromLevelsData = () => {
    const newValue = buildCategoryValue()
    currentValue.value = newValue
  }

  // 完成选择
  const completeSelection = (value: CategoryPickerValue) => {
    currentValue.value = value
    emit('update:modelValue', value)
    emit('change', value)
  }

  // 处理多选
  const handleMultipleSelection = (category: ICategory, levelData: LevelData) => {
    if (!levelData.selectedIds) {
      levelData.selectedIds = []
    }

    const selectedIds = levelData.selectedIds
    const categoryId = category.id || ''
    const index = selectedIds.indexOf(categoryId)

    if (index > -1) {
      // 取消选择
      selectedIds.splice(index, 1)
    } else {
      // 添加选择
      selectedIds.push(categoryId)
    }
  }

  // 处理分类项点击（用于非最后一层）
  const handleCategoryClick = async (category: ICategory, levelData: LevelData) => {
    console.log('handleCategoryClick', category, levelData)

    // 如果是最后一层且支持多选，使用多选逻辑
    if (isLastLevelByNumber(levelData.level) && isMultipleMode.value) {
      handleMultipleSelection(category, levelData)
      return
    }

    // 单选模式：如果点击的是当前层级已经选中的节点
    if (levelData.selectedId === category.id) {
      // 如果是最后一层，允许取消选择（适配CheckBox行为）
      if (isLastLevelByNumber(levelData.level)) {
        levelData.selectedId = undefined
      }
      return
    }

    // 如果是最后一层的单选模式，直接设置选中项（不需要加载后续层级）
    if (isLastLevelByNumber(levelData.level)) {
      // 最后一层单选：清空同层级其他选项，只选中当前项
      levelData.selectedId = category.id
      // 清空多选状态（如果有的话）
      levelData.selectedIds = undefined
      return
    }

    try {
      loading.value = true
      error.value = ''

      // 单选模式 - 清除当前层级之后的所有层级数据
      const currentLevelIndex = levelsData.value.findIndex(ld => ld.level === levelData.level)
      if (currentLevelIndex >= 0) {
        // 清除后续层级的选择状态和数据
        levelsData.value = levelsData.value.slice(0, currentLevelIndex + 1)
      }

      // 设置当前层级的选中项
      levelData.selectedId = category.id

      // 如果不是叶子节点且未达到最大层级，继续加载并自动选择后续层级
      // 注意：最后一层不再查找子元素
      if (!category.isLeaf && (maxLevels.value === 0 || levelData.level < maxLevels.value)) {
        await loadAndAutoSelectNextLevels(category.id || '', levelData.level)
      }
    } catch (err: any) {
      error.value = err.message || '加载分类数据失败'
    } finally {
      loading.value = false
    }
  }

  // 加载并自动选择后续层级
  const loadAndAutoSelectNextLevels = async (categoryId: string, currentLevel: number) => {
    let parentId = categoryId
    let level = currentLevel

    while (level < maxLevels.value - 1) {
      const nextLevel = level + 1

      // 检查是否达到最大层级限制
      if (maxLevels.value > 0 && nextLevel > maxLevels.value) {
        break
      }

      try {
        // eslint-disable-next-line no-await-in-loop
        const response = await getChildrenCategories({ categoryId: parentId })

        if (!response.childrenInfos || response.childrenInfos.length === 0) {
          // 没有子分类，停止
          break
        }

        // 创建下一层数据
        const nextLevelData: LevelData = {
          level: nextLevel,
          categories: response.childrenInfos,
          selectedId: undefined,
          selectedIds: undefined
        }

        // 添加到层级数据中
        levelsData.value.push(nextLevelData)

        // 自动选中第一个选项
        const firstCategory = response.childrenInfos[0]
        nextLevelData.selectedId = firstCategory.id

        // 如果第一个选项是叶子节点或已达到最大层级，停止继续加载
        if (firstCategory.isLeaf || (maxLevels.value > 0 && nextLevel >= maxLevels.value)) {
          break
        }

        // 准备下一轮循环
        parentId = firstCategory.id || ''
        level = nextLevel
      } catch (err) {
        // 加载失败，停止
        console.warn('加载子分类失败:', err)
        break
      }
    }
  }

  // 返回上一层
  const goBack = () => {
    if (currentLevel.value > 0) {
      currentLevel.value -= 1
      // 清除当前层及之后的选择状态
      for (let i = currentLevel.value + 1; i < levelsData.value.length; i += 1) {
        levelsData.value[i].selectedId = undefined
        levelsData.value[i].selectedIds = undefined
      }
      // 移除后续层级数据
      levelsData.value = levelsData.value.slice(0, currentLevel.value + 1)
    }
  }

  // 确认选择
  const handleConfirm = () => {
    let value: CategoryPickerValue = []

    if (isMultipleMode.value) {
      value = buildMultipleValue()
    } else {
      // 单选模式，构建当前选择的值
      value = buildCategoryValue()
    }

    // 执行双向绑定
    completeSelection(value)

    // 触发 confirm 事件
    emit('confirm', value)

    visible.value = false
  }

  // 取消选择
  const handleCancel = () => {
    visible.value = false

    // 恢复到确认过的状态，不保存临时修改
    if (props.modelValue && props.modelValue.length > 0) {
      // 如果有确认过的值，恢复到那个状态
      currentValue.value = [...props.modelValue]
    } else {
      // 如果没有确认过的值，清空临时状态
      currentValue.value = []
    }

    // 清空层级数据，下次打开时重新加载
    levelsData.value = []
    currentLevel.value = 0
  }

  // 重试加载数据
  const retryLoad = async () => {
    error.value = ''
    await initializeData()
  }

  // 显示选择器
  const showPicker = async () => {
    if (!props.disabled && !props.readonly) {
      visible.value = true

      // 根据 props.modelValue 决定如何加载数据
      if (props.modelValue && props.modelValue.length > 0) {
        // 基于确认过的值初始化
        await initializeFromValue(props.modelValue)
      } else {
        // 没有确认过的值，加载初始数据
        await initializeData()
      }
    }
  }

  // 面包屑导航
  const breadcrumbs = computed(() => {
    const crumbs: { level: number; name: string; active: boolean }[] = []

    levelsData.value.forEach((levelData, index) => {
      if (levelData.selectedId) {
        const category = levelData.categories.find(cat => cat.id === levelData.selectedId)
        if (category) {
          crumbs.push({
            level: index,
            name: category.name!,
            active: index === currentLevel.value
          })
        }
      }
    })

    return crumbs
  })

  return {
    loading,
    error,
    visible,
    displayText,
    currentValue,
    currentLevel,
    isMultipleMode,
    gridColumns,
    breadcrumbs,
    handleCategoryClick,
    handleConfirm,
    handleCancel,
    showPicker,
    retryLoad,
    goBack,
    levelsData,
    maxLevels,
    isLastLevelByNumber
  }
}
