<template>
  <div class="test-example">
    <h2>CategoryPicker 优化测试</h2>
    
    <div class="test-section">
      <h3>测试新的数据结构</h3>
      <CategoryPicker
        v-model="testValue"
        placeholder="请选择分类"
        title="测试分类选择"
        @change="handleChange"
        @confirm="handleConfirm"
      />
      
      <div class="result">
        <h4>选中的值（新格式）：</h4>
        <pre>{{ JSON.stringify(testValue, null, 2) }}</pre>
      </div>
      
      <div class="display-text">
        <h4>显示文本：</h4>
        <p>{{ displayText }}</p>
      </div>
      
      <div class="actions">
        <button @click="setTestValue">设置测试值</button>
        <button @click="clearValue">清空值</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import CategoryPicker from './index.vue'
import type { CategoryPickerValue } from './types'

// 测试数据
const testValue = ref<CategoryPickerValue | null>(null)

// 计算显示文本（模拟组件内部逻辑）
const displayText = computed(() => {
  if (!testValue.value || testValue.value.length === 0) {
    return '请选择'
  }
  return testValue.value.map(item => item.name).join(' / ')
})

// 事件处理
const handleChange = (value: CategoryPickerValue) => {
  console.log('Change事件:', value)
}

const handleConfirm = (value: CategoryPickerValue) => {
  console.log('Confirm事件:', value)
  console.log('显示文本应该是:', value.map(item => item.name).join(' / '))
}

// 测试方法
const setTestValue = () => {
  testValue.value = [
    { id: '1', name: '美食' },
    { id: '2', name: '川菜' },
    { id: '3', name: '火锅' }
  ]
}

const clearValue = () => {
  testValue.value = null
}
</script>

<style scoped lang="stylus">
.test-example
  padding 20px
  max-width 600px
  margin 0 auto

.test-section
  margin-bottom 30px
  padding 20px
  border 1px solid #e0e0e0
  border-radius 8px

h2, h3, h4
  color #333
  margin-bottom 15px

.result, .display-text
  margin-top 15px
  padding 10px
  background #f5f5f5
  border-radius 4px

pre
  font-size 12px
  color #666
  white-space pre-wrap
  word-break break-all

.actions
  margin-top 15px

button
  margin-right 10px
  padding 8px 16px
  background #1890ff
  color white
  border none
  border-radius 4px
  cursor pointer

  &:hover
    background #40a9ff
</style>
