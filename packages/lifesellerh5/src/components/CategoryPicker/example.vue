<template>
  <div class="example-page">
    <h1>CategoryPicker 组件示例</h1>

    <div class="description">
      <h3>新功能特性：</h3>
      <ul>
        <li>🎯 <strong>Grid布局</strong>：按层级分列显示，支持横向比较选择</li>
        <li>🚀 <strong>智能预选</strong>：初始化时自动选中每层第一个选项</li>
        <li>🔄 <strong>级联自动选择</strong>：选择任一分类后自动选中下级第一个选项</li>
        <li>✅ <strong>确认模式</strong>：点击分类仅做选中，需点击确认按钮才执行双向绑定</li>
        <li>🔢 <strong>层级限制</strong>：支持设置最大层级数，到达最后一层时不再查找子元素</li>
        <li>⚡ <strong>懒加载</strong>：只在浮层打开时才请求接口数据</li>
        <li>📱 <strong>移动优化</strong>：针对移动端优化的交互体验</li>
      </ul>
    </div>

    <!-- 基础用法 -->
    <div class="example-section">
      <h2>基础用法</h2>
      <CategoryPicker
        v-model="basicValue"
        placeholder="请选择分类"
        title="选择分类"
        @change="handleBasicChange"
      />
      <div class="result">
        选中值: {{ JSON.stringify(basicValue, null, 2) }}
      </div>
    </div>

    <!-- 多选模式 -->
    <div class="example-section">
      <h2>多选模式（最后一层支持多选）</h2>
      <CategoryPicker
        v-model="multipleValue"
        placeholder="请选择分类（支持多选）"
        title="选择分类（多选）"
        :last-level-multiple="true"
        @change="handleMultipleChange"
      />
      <div class="result">
        选中值: {{ JSON.stringify(multipleValue, null, 2) }}
      </div>
    </div>

    <!-- 自定义层级数 -->
    <div class="example-section">
      <h2>自定义最大层级数</h2>
      <CategoryPicker
        v-model="customLevelValue"
        placeholder="最多2级分类"
        title="选择分类（最多2级）"
        :max-levels="2"
        @change="handleCustomLevelChange"
      />
      <div class="result">
        选中值: {{ JSON.stringify(customLevelValue, null, 2) }}
      </div>
    </div>

    <!-- Grid布局展示 -->
    <div class="example-section">
      <h2>Grid布局按层级分列</h2>
      <CategoryPicker
        v-model="gridValue"
        placeholder="层级分列显示"
        title="选择分类（层级分列）"
        @change="handleGridChange"
      />
      <div class="result">
        选中值: {{ JSON.stringify(gridValue, null, 2) }}
      </div>
    </div>

    <!-- 自动选择默认值 -->
    <div class="example-section">
      <h2>自动选择每层第一个</h2>
      <CategoryPicker
        v-model="autoSelectValue"
        placeholder="打开时自动选中每层第一个"
        title="自动选择演示"
        @change="handleAutoSelectChange"
      />
      <button @click="clearAutoSelect">清空重新测试</button>
      <div class="result">
        选中值: {{ JSON.stringify(autoSelectValue, null, 2) }}
      </div>
    </div>

    <!-- 级联自动选择演示 -->
    <div class="example-section">
      <h2>确认模式演示</h2>
      <p class="section-desc">点击分类只做选中操作，选择任一分类后系统会自动选中下级分类的第一个选项。只有点击"确认选择"按钮后才会执行数据双向绑定。</p>
      <CategoryPicker
        v-model="cascadeValue"
        placeholder="体验确认模式和级联自动选择"
        title="确认模式演示"
        @change="handleCascadeChange"
      />
      <button @click="clearCascade">重置</button>
      <div class="result">
        选中值: {{ JSON.stringify(cascadeValue, null, 2) }}
      </div>
    </div>

    <!-- 最大层级限制演示 -->
    <div class="example-section">
      <h2>最大层级限制演示</h2>
      <p class="section-desc">设置最大层级为2，到达最后一层时不再查找子元素，即使该分类还有下级分类。</p>
      <CategoryPicker
        v-model="maxLevelValue"
        placeholder="最多2层级别"
        title="最大层级演示"
        :max-levels="2"
        @change="handleMaxLevelChange"
      />
      <button @click="clearMaxLevel">重置</button>
      <div class="result">
        选中值: {{ JSON.stringify(maxLevelValue, null, 2) }}
      </div>
    </div>

    <!-- 禁用状态 -->
    <div class="example-section">
      <h2>禁用状态</h2>
      <CategoryPicker
        v-model="disabledValue"
        placeholder="禁用状态"
        title="选择分类"
        :disabled="true"
      />
    </div>

    <!-- 只读状态 -->
    <div class="example-section">
      <h2>只读状态</h2>
      <CategoryPicker
        v-model="readonlyValue"
        placeholder="只读状态"
        title="选择分类"
        :readonly="true"
      />
    </div>

    <!-- 带默认值 -->
    <div class="example-section">
      <h2>带默认值</h2>
      <CategoryPicker
        v-model="defaultValue"
        placeholder="带默认值"
        title="选择分类"
        @change="handleDefaultChange"
      />
      <button @click="setDefaultValue">设置默认值</button>
      <button @click="clearDefaultValue">清空值</button>
      <div class="result">
        选中值: {{ JSON.stringify(defaultValue, null, 2) }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue'
  import CategoryPicker from './index.vue'
  import type { CategoryPickerValue } from './types'

  // 基础用法
  const basicValue = ref<CategoryPickerValue | null>(null)
  const handleBasicChange = (value: CategoryPickerValue | null) => {
    console.log('基础用法变化:', value)
  }

  // 多选模式
  const multipleValue = ref<CategoryPickerValue | null>(null)
  const handleMultipleChange = (value: CategoryPickerValue | null) => {
    console.log('多选模式变化:', value)
  }

  // 自定义层级数
  const customLevelValue = ref<CategoryPickerValue | null>(null)
  const handleCustomLevelChange = (value: CategoryPickerValue | null) => {
    console.log('自定义层级变化:', value)
  }

  // Grid布局展示
  const gridValue = ref<CategoryPickerValue | null>(null)
  const handleGridChange = (value: CategoryPickerValue | null) => {
    console.log('Grid布局变化:', value)
  }

  // 自动选择演示
  const autoSelectValue = ref<CategoryPickerValue | null>(null)
  const handleAutoSelectChange = (value: CategoryPickerValue | null) => {
    console.log('自动选择变化:', value)
  }

  const clearAutoSelect = () => {
    autoSelectValue.value = null
  }

  // 确认模式演示
  const cascadeValue = ref<CategoryPickerValue | null>(null)
  const handleCascadeChange = (value: CategoryPickerValue | null) => {
    console.log('确认模式变化:', value)
  }

  const clearCascade = () => {
    cascadeValue.value = null
  }

  // 禁用状态
  const disabledValue = ref<CategoryPickerValue | null>([
    { id: '64240ea443a20f0001e74f7a', name: '美食' },
    { id: '64240ea443a20f0001e74f7b', name: '东南亚菜' }
  ])

  // 只读状态
  const readonlyValue = ref<CategoryPickerValue | null>([
    { id: '64240ea443a20f0001e74f7a', name: '美食' },
    { id: '64240ea443a20f0001e74f7b', name: '东南亚菜' },
    { id: '64240ea443a20f0001e74f7c', name: '泰国菜' }
  ])

  // 带默认值
  const defaultValue = ref<CategoryPickerValue | null>(null)
  const handleDefaultChange = (value: CategoryPickerValue | null) => {
    console.log('默认值变化:', value)
  }

  const setDefaultValue = () => {
    defaultValue.value = [
      { id: '64240ea443a20f0001e74f7a', name: '美食' },
      { id: '64240f587d13e90001fa7d00', name: '川菜' },
      { id: '64240f7b7d13e90001fa7d01', name: '火锅' }
    ]
  }

  const clearDefaultValue = () => {
    defaultValue.value = null
  }

  // 最大层级限制演示
  const maxLevelValue = ref<CategoryPickerValue | null>(null)
  const handleMaxLevelChange = (value: CategoryPickerValue | null) => {
    console.log('最大层级变化:', value)
  }

  const clearMaxLevel = () => {
    maxLevelValue.value = null
  }
</script>

<style scoped lang="stylus">
.example-page
  padding 20px
  max-width 800px
  margin 0 auto

h1
  color #333
  margin-bottom 30px

h2
  color #666
  margin-bottom 15px
  font-size 18px

h3
  color #333
  margin-bottom 10px
  font-size 16px

.description
  margin-bottom 30px
  padding 15px
  background linear-gradient(135deg, #667eea 0%, #764ba2 100%)
  color white
  border-radius 8px

  ul
    margin 10px 0 0 20px

  li
    margin-bottom 8px
    line-height 1.5

  strong
    font-weight 600

.example-section
  margin-bottom 40px
  padding 20px
  border 1px solid #e0e0e0
  border-radius 8px
  background #fafafa

.section-desc
  margin-bottom 15px
  padding 10px
  background #e8f4fd
  border-left 4px solid #1890ff
  color #666
  font-size 14px
  line-height 1.5

.result
  margin-top 15px
  padding 10px
  background #f5f5f5
  border-radius 4px
  font-size 12px
  color #666
  white-space pre-wrap
  word-break break-all

button
  margin-right 10px
  margin-top 10px
  padding 8px 16px
  background #1890ff
  color white
  border none
  border-radius 4px
  cursor pointer

  &:hover
    background #40a9ff
</style>
